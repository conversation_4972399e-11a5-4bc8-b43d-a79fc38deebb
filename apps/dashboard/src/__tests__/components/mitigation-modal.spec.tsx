import '@testing-library/jest-dom/vitest'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { MemoryRouter } from 'react-router'
import { MitigationModal } from '../../pages/documents-details/components/mitigation-modal'
import { Providers } from '../../components/providers'
import type { DesignDocTopPolicyRecommendation } from 'prime-front-service-client'
import { useCreateMitigation } from '../../api/use-mitigations-api'

// Mock the API hook
vi.mock('../../api/use-mitigations-api', () => ({
  useCreateMitigation: vi.fn(),
}))

// Mock the toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}))

// Mock i18next
vi.mock('i18next', () => ({
  t: (key: string) => key,
}))

describe('MitigationModal Component', () => {
  const mockMutate = vi.fn()
  const mockRecommendations: DesignDocTopPolicyRecommendation[] = [
    {
      id: 1,
      title: 'Test Recommendation 1',
      description: 'Test description 1',
      policy_id: 'policy-1',
      policy_title: 'Test Policy 1',
      severity: 'high',
      category: 'security',
    },
    {
      id: 2,
      title: 'Test Recommendation 2',
      description: 'Test description 2',
      policy_id: 'policy-2',
      policy_title: 'Test Policy 2',
      severity: 'medium',
      category: 'compliance',
    },
  ]

  const defaultProps = {
    docId: 123,
    recommendations: mockRecommendations,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useCreateMitigation as any).mockReturnValue({
      mutate: mockMutate,
      isPending: false,
    })
  })

  it('renders the trigger button when recommendations exist', () => {
    render(
      <MemoryRouter>
        <MitigationModal {...defaultProps} />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    expect(screen.getByTestId('mitigation-modal-button')).toBeInTheDocument()
    expect(screen.getByText('createMitigationPlan')).toBeInTheDocument()
  })

  it('does not render trigger button when no recommendations', () => {
    render(
      <MemoryRouter>
        <MitigationModal {...defaultProps} recommendations={[]} />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    expect(screen.queryByTestId('mitigation-modal-button')).not.toBeInTheDocument()
  })

  it('opens modal when trigger button is clicked', async () => {
    render(
      <MemoryRouter>
        <MitigationModal {...defaultProps} />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    fireEvent.click(screen.getByTestId('mitigation-modal-button'))

    await waitFor(() => {
      expect(screen.getByText('mitigationPlan')).toBeInTheDocument()
      expect(screen.getByText('mitigationPlanDescription')).toBeInTheDocument()
    })
  })

  it('renders all modular components when modal is open', async () => {
    render(
      <MemoryRouter>
        <MitigationModal {...defaultProps} />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    fireEvent.click(screen.getByTestId('mitigation-modal-button'))

    await waitFor(() => {
      // Check that the modal content is rendered
      expect(screen.getByText('mitigationPlan')).toBeInTheDocument()
      
      // Check that the submit button is present
      expect(screen.getByTestId('submit-mitigation-plan')).toBeInTheDocument()
      
      // Check that the submit button is disabled when no items are selected
      expect(screen.getByTestId('submit-mitigation-plan')).toBeDisabled()
    })
  })

  it('enables submit button when items are selected', async () => {
    render(
      <MemoryRouter>
        <MitigationModal {...defaultProps} />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    fireEvent.click(screen.getByTestId('mitigation-modal-button'))

    await waitFor(() => {
      expect(screen.getByText('mitigationPlan')).toBeInTheDocument()
    })

    // Find and click a checkbox to select an item
    const checkboxes = screen.getAllByRole('checkbox')
    if (checkboxes.length > 0) {
      fireEvent.click(checkboxes[0])
      
      await waitFor(() => {
        expect(screen.getByTestId('submit-mitigation-plan')).not.toBeDisabled()
      })
    }
  })

  it('shows loading spinner when creating mitigation', async () => {
    ;(useCreateMitigation as any).mockReturnValue({
      mutate: mockMutate,
      isPending: true,
    })

    render(
      <MemoryRouter>
        <MitigationModal {...defaultProps} />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    fireEvent.click(screen.getByTestId('mitigation-modal-button'))

    await waitFor(() => {
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    })
  })

  it('calls createMitigation with correct data when submitted', async () => {
    render(
      <MemoryRouter>
        <MitigationModal {...defaultProps} />
      </MemoryRouter>,
      {
        wrapper: Providers,
      }
    )

    fireEvent.click(screen.getByTestId('mitigation-modal-button'))

    await waitFor(() => {
      expect(screen.getByText('mitigationPlan')).toBeInTheDocument()
    })

    // Select an item and submit
    const checkboxes = screen.getAllByRole('checkbox')
    if (checkboxes.length > 0) {
      fireEvent.click(checkboxes[0])
      
      await waitFor(() => {
        const submitButton = screen.getByTestId('submit-mitigation-plan')
        expect(submitButton).not.toBeDisabled()
        fireEvent.click(submitButton)
      })

      expect(mockMutate).toHaveBeenCalledWith(
        expect.objectContaining({
          security_review_id: 123,
          CreateMitigationsRequest: expect.objectContaining({
            mitigations_data: expect.any(Array),
            should_export_to_csv: expect.any(Boolean),
            should_export_to_pdf: expect.any(Boolean),
            issue_id_write_back: null,
          }),
        }),
        expect.any(Object)
      )
    }
  })
})
