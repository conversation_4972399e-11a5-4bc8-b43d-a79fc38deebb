/* eslint-disable max-lines */
'use client'

import { use<PERSON><PERSON>back, useEffect, useState } from 'react'
import {
  CheckIcon,
  ChevronDownIcon,
  ChevronsUpDown,
  Loader2Icon,
  Plus,
  XIcon,
} from 'lucide-react'
import { format } from 'date-fns'
import {
  Button,
  Calendar,
  Checkbox,
  Command,
  CommandGroup,
  CommandInput,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Input,
  Label,
  Popover,
  PopoverContent,
  PopoverTrigger,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Separator,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Textarea,
  useDebounce,
} from '@libs/ui'
import { cn } from '@libs/common'
import type {
  CreateMitigationDataRequest,
  DesignDocTopPolicyRecommendation,
  MitigationPriority,
} from 'prime-front-service-client'
import { useCreateMitigation } from '../../../api/use-mitigations-api'
import { toast } from 'sonner'
import { t } from 'i18next'
import { useQuery } from '@tanstack/react-query'
import { casesAPI } from '../../../api/clients'

interface MitigationModalProps {
  docId: number
  recommendations: DesignDocTopPolicyRecommendation[]
}

export interface MitigationItem {
  id: number
  title: string
  description: string
  assignee: string | null
  due_date: Date | null
  priority: MitigationPriority | null
  selected: boolean
}

interface SelectedCaseProps {
  id: number
  title: string
  issue_id: string
}

const AUTOCOMPLETE_LIMIT = 20

export const MitigationModal = ({
  recommendations,
  docId,
}: MitigationModalProps) => {
  const { mutate: createMitigation, isPending: isCreatingMitigation } =
    useCreateMitigation()

  const [caseSearchValue, setCaseSearchValue] = useState('')
  const debouncedCaseSearch = useDebounce(caseSearchValue, 300)
  const [selectedCase, setSelectedCase] = useState<SelectedCaseProps | null>(
    null
  )

  const { data: casesSearchResults, isLoading: isSearchingCases } = useQuery({
    queryKey: ['searchCases', debouncedCaseSearch, 10],
    queryFn: async () => {
      if (!debouncedCaseSearch.trim()) return []
      try {
        return await casesAPI.autocompleteSearchGlobalCases({
          value: debouncedCaseSearch,
          limit: AUTOCOMPLETE_LIMIT,
        })
      } catch (error) {
        toast.error('Failed to search cases')
        throw new Error('Failed to search cases')
      }
    },
    enabled: debouncedCaseSearch.trim().length > 0,
    refetchOnWindowFocus: false,
    retry: false,
  })

  const [open, setOpen] = useState(false)
  const [openSelectTicket, setOpenSelectTicket] = useState(false)

  const [items, setItems] = useState<MitigationItem[]>([])
  const [selectedItemIds, setSelectedItemIds] = useState<number[]>([])

  const [exportToPdf, setExportToPdf] = useState(false)
  const [exportToCsv, setExportToCsv] = useState(false)
  const [commentInJira, setCommentInJira] = useState(false)

  const [showAddCustom, setShowAddCustom] = useState(false)
  const [customTitle, setCustomTitle] = useState('')
  const [customDescription, setCustomDescription] = useState('')
  const [customAssignee, setCustomAssignee] = useState('')
  const [customDueDate, setCustomDueDate] = useState<Date | null>(null)
  const [customPriority, setCustomPriority] =
    useState<MitigationPriority | null>(null)

  const updateItem = useCallback(
    (id: number, field: keyof MitigationItem, value: any) => {
      setItems(
        items.map((item) =>
          item.id === id ? { ...item, [field]: value } : item
        )
      )
    },
    [items]
  )

  const toggleSelection = (id: number) => {
    setSelectedItemIds((prev) =>
      prev.includes(id) ? prev.filter((i) => i !== id) : [...prev, id]
    )
  }

  const handleSaveCustomRecommendation = () => {
    const newId = Math.max(...items.map((item) => item.id), 0) + 1
    const newItem: MitigationItem = {
      id: newId,
      title: customTitle.trim(),
      description: customDescription.trim(),
      assignee: customAssignee.trim() || null,
      due_date: customDueDate,
      priority: customPriority,
      selected: false,
    }

    setItems([...items, newItem])
    setCustomTitle('')
    setCustomDescription('')
    setCustomAssignee('')
    setCustomDueDate(null)
    setCustomPriority(null)
    setShowAddCustom(false)
  }

  const handleCancelCustomRecommendation = () => {
    setCustomTitle('')
    setCustomDescription('')
    setCustomAssignee('')
    setCustomDueDate(null)
    setCustomPriority(null)
    setShowAddCustom(false)
  }

  const handleCreateMitigation = () => {
    const mitigationsData: CreateMitigationDataRequest[] = items
      .filter((item) => selectedItemIds.includes(item.id))
      .map((item) => ({
        description: item.description,
        title: item.title,
        assignee: item.assignee || null,
        priority: item.priority || null,
        due_date: item.due_date || null,
      }))

    createMitigation(
      {
        security_review_id: docId,
        CreateMitigationsRequest: {
          mitigations_data: mitigationsData,
          should_export_to_csv: exportToCsv,
          should_export_to_pdf: exportToPdf,
          issue_id_write_back: selectedCase?.issue_id || null,
        },
      },
      {
        onSuccess: async (response: any) => {
          const blob = await response.blob()
          const fileType = blob.type.includes('zip')
            ? 'zip'
            : blob.type.includes('pdf')
            ? 'pdf'
            : blob.type.includes('csv')
            ? 'csv'
            : null

          if (fileType) {
            const filename = `mitigation_plan_${docId}.${fileType}`

            const url = window.URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = filename
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            window.URL.revokeObjectURL(url)
          }

          setOpen(false)
          toast.success(t('mitigationPlanCreatedSuccessfully'))
        },
        onError: () => {
          setOpen(false)
          toast.error(t('errors.failedToCreateMitigationPlan'))
        },
      }
    )
  }

  useEffect(() => {
    setItems(
      recommendations.map((recommendation) => ({
        id: recommendation.id,
        title: recommendation.title,
        description: recommendation.description,
        assignee: null,
        due_date: null,
        priority: null,
        selected: false,
      }))
    )
  }, [recommendations])

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {!!recommendations.length && (
        <DialogTrigger asChild>
          <Button dataTestId="mitigation-modal-button">
            {t('createMitigationPlan')}
          </Button>
        </DialogTrigger>
      )}

      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t('mitigationPlan')}</DialogTitle>
          <DialogDescription>
            {t('mitigationPlanDescription')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Table>
            <TableHeader>
              <TableRow className="capitalize">
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedItemIds.length === items.length}
                    onCheckedChange={(value) => {
                      setSelectedItemIds(
                        value ? items.map((item) => item.id) : []
                      )
                    }}
                  />
                </TableHead>
                <TableHead className="w-48">{t('title')}</TableHead>
                <TableHead>{t('description')}</TableHead>
                <TableHead className="w-40">{t('assignee')}</TableHead>
                <TableHead className="w-40">{t('dueDate')}</TableHead>
                <TableHead className="w-32">{t('priority')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedItemIds.includes(item.id)}
                      onCheckedChange={() => toggleSelection(item.id)}
                    />
                  </TableCell>
                  <TableCell className="font-medium">{item.title}</TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {item.description}
                  </TableCell>
                  <TableCell>
                    <Input
                      placeholder="Assignee"
                      value={item.assignee || ''}
                      onChange={(e) =>
                        updateItem(item.id, 'assignee', e.target.value)
                      }
                    />
                  </TableCell>
                  <TableCell>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          dataTestId="due-date-button"
                          variant="outline"
                          className={cn(
                            'w-full font-normal justify-between',
                            !item.due_date && 'text-muted-foreground'
                          )}
                        >
                          {item.due_date
                            ? format(item.due_date, 'PPP')
                            : 'Date'}

                          <ChevronDownIcon className="h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          {...({
                            mode: 'single',
                            selected: item.due_date,
                            onSelect: (date: Date | undefined) =>
                              updateItem(item.id, 'due_date', date || null),
                            initialFocus: true,
                          } as any)}
                        />
                      </PopoverContent>
                    </Popover>
                  </TableCell>
                  <TableCell>
                    <Select
                      value={item.priority || ''}
                      onValueChange={(value) =>
                        updateItem(item.id, 'priority', value)
                      }
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          <Separator />

          {!showAddCustom ? (
            <Button
              dataTestId="add-custom-recommendation"
              variant="outline"
              className="w-fit"
              onClick={() => setShowAddCustom(true)}
            >
              <Plus className="mr-2 h-4 w-4" />
              {t('addCustomRecommendation')}
            </Button>
          ) : (
            <div className="border rounded-lg p-4 space-y-4">
              <div className="grid grid-cols-12 gap-4 items-start">
                <div className="col-span-2">
                  <Textarea
                    placeholder="Title"
                    value={customTitle}
                    onChange={(e) => setCustomTitle(e.target.value)}
                    className="w-full min-h-[80px] resize-none"
                  />
                </div>

                <div className="col-span-4">
                  <Textarea
                    placeholder="Description"
                    value={customDescription}
                    onChange={(e) => setCustomDescription(e.target.value)}
                    className="w-full min-h-[80px] resize-none"
                  />
                </div>

                <div className="col-span-2">
                  <Input
                    placeholder="Assignee"
                    value={customAssignee}
                    onChange={(e) => setCustomAssignee(e.target.value)}
                    className="w-full"
                  />
                </div>

                <div className="col-span-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        dataTestId="custom-due-date-button"
                        variant="outline"
                        className={cn(
                          'w-full font-normal justify-between',
                          !customDueDate && 'text-muted-foreground'
                        )}
                      >
                        {customDueDate ? format(customDueDate, 'PPP') : 'Date'}

                        <ChevronDownIcon className="h-4 w-4" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        {...({
                          mode: 'single',
                          selected: customDueDate,
                          onSelect: (date: Date | undefined) =>
                            setCustomDueDate(date || null),
                          initialFocus: true,
                        } as any)}
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="col-span-2">
                  <Select
                    value={customPriority || ''}
                    onValueChange={(value) =>
                      setCustomPriority(value as MitigationPriority)
                    }
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleCancelCustomRecommendation}
                  className="h-8 w-8 p-0"
                  dataTestId="cancel-custom-recommendation"
                >
                  <XIcon className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  onClick={handleSaveCustomRecommendation}
                  className="h-8 w-8 p-0"
                  disabled={!customTitle.trim() || !customDescription.trim()}
                  dataTestId="save-custom-recommendation"
                >
                  <CheckIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          <div className="pt-4">
            <Label className="text-sm font-bold uppercase">
              {t('actions')}
            </Label>
            <div className="flex flex-wrap gap-4 mt-2">
              <div className="flex items-center gap-3">
                <Checkbox
                  id="export-pdf"
                  checked={exportToPdf}
                  onCheckedChange={(checked) => setExportToPdf(!!checked)}
                />
                <Label htmlFor="export-pdf">{t('exportToPdf')}</Label>
              </div>

              <div className="flex items-center gap-3">
                <Checkbox
                  id="export-csv"
                  checked={exportToCsv}
                  onCheckedChange={(checked) => setExportToCsv(!!checked)}
                />
                <Label htmlFor="export-csv">{t('exportToCsv')}</Label>
              </div>
              <div className="flex items-center gap-3">
                <Checkbox
                  id="comment-jira"
                  checked={commentInJira}
                  onCheckedChange={(checked) => setCommentInJira(!!checked)}
                />
                <Label htmlFor="comment-jira">{t('commentInJira')}</Label>
              </div>
              <div>
                <Popover
                  open={openSelectTicket}
                  onOpenChange={setOpenSelectTicket}
                  modal={true}
                >
                  <PopoverTrigger asChild>
                    <Button
                      dataTestId="select-framework-button"
                      variant="outline"
                      role="combobox"
                      aria-expanded={open}
                      size="sm"
                      className="w-[140px] justify-between"
                      disabled={!commentInJira}
                    >
                      {selectedCase?.issue_id || t('selectTicket')}
                      <ChevronsUpDown className="h-4 w-4" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[300px] p-0">
                    <Command shouldFilter={false}>
                      <CommandInput
                        placeholder={t('searchCases')}
                        value={caseSearchValue}
                        onValueChange={setCaseSearchValue}
                      />
                      <CommandGroup className="max-h-[300px] overflow-y-auto">
                        {isSearchingCases ? (
                          <div className="p-6 text-center text-muted-foreground">
                            Loading...
                          </div>
                        ) : casesSearchResults?.length ? (
                          <RadioGroup
                            value={selectedCase?.id?.toString() || ''}
                            onValueChange={(value) => {
                              const c = casesSearchResults?.find(
                                (c) => c.id.toString() === value
                              )
                              if (c) {
                                setSelectedCase({
                                  id: c.id,
                                  title: c.title?.trim() || 'Untitled',
                                  issue_id: c.issue_id,
                                })
                              }
                            }}
                            className="p-2"
                          >
                            {casesSearchResults?.map((c) => {
                              const name = c.title?.trim() || 'Untitled'
                              const caseId = c.id

                              return (
                                <div
                                  key={caseId.toString()}
                                  className="flex items-center space-x-2"
                                >
                                  <RadioGroupItem
                                    value={caseId.toString()}
                                    id={caseId.toString()}
                                  />
                                  <Label
                                    htmlFor={caseId.toString()}
                                    className="flex flex-col cursor-pointer"
                                  >
                                    <span className="text-sm">{name}</span>
                                    <span className="text-xs text-muted-foreground">
                                      {c.issue_id}
                                    </span>
                                  </Label>
                                </div>
                              )
                            })}
                          </RadioGroup>
                        ) : debouncedCaseSearch.trim().length > 0 ? (
                          <div className="p-6 text-center text-sm">
                            {t('noTicketsFound')}
                          </div>
                        ) : (
                          <div className="p-2 text-center text-muted-foreground text-xs truncate">
                            {t('startTypingToSearchTickets')}
                          </div>
                        )}
                      </CommandGroup>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            onClick={handleCreateMitigation}
            dataTestId="submit-mitigation-plan"
            disabled={isCreatingMitigation || selectedItemIds.length === 0}
          >
            {isCreatingMitigation && (
              <Loader2Icon
                className="animate-spin"
                data-testid="loading-spinner"
                size={20}
              />
            )}
            {t('submit')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
