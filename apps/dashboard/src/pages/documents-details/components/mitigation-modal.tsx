'use client'
import { useState } from 'react'
import { Loader2Icon } from 'lucide-react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
  <PERSON>alogTrigger,
  Separator,
} from '@libs/ui'
import type { CreateMitigationDataRequest } from 'prime-front-service-client'
import { useCreateMitigation } from '../../../api/use-mitigations-api'
import { toast } from 'sonner'
import { t } from 'i18next'
import type { MitigationModalProps } from './mitigation-modal/types'
import {
  useMitigationItems,
  useCaseSearch,
  useExportOptions,
} from './mitigation-modal/hooks'
import {
  MitigationTable,
  AddCustomRecommendation,
  ExportActions,
} from './mitigation-modal/components'
import { useNavigate } from 'react-router'

export const MitigationModal = ({
  recommendations,
  docId,
}: MitigationModalProps) => {
  const { mutate: createMitigation, isPending: isCreatingMitigation } =
    useCreateMitigation()
  const navigate = useNavigate()
  const [open, setOpen] = useState(false)

  const mitigationItems = useMitigationItems({ recommendations })
  const caseSearch = useCaseSearch()
  const { exportOptions, updateExportOption } = useExportOptions()

  const handleCreateMitigation = () => {
    const selectedItems = mitigationItems.getSelectedItems()
    const mitigationsData: CreateMitigationDataRequest[] = selectedItems.map(
      (item) => ({
        description: item.description,
        title: item.title,
        assignee: item.assignee || null,
        priority: item.priority || null,
        due_date: item.due_date || null,
      })
    )

    createMitigation(
      {
        security_review_id: docId,
        CreateMitigationsRequest: {
          mitigations_data: mitigationsData,
          should_export_to_csv: exportOptions.exportToCsv,
          should_export_to_pdf: exportOptions.exportToPdf,
          issue_id_write_back: caseSearch.selectedCase?.issue_id || null,
        },
      },
      {
        onSuccess: async (response: Response) => {
          const blob = await response.blob()
          const fileType = blob.type.includes('zip')
            ? 'zip'
            : blob.type.includes('pdf')
            ? 'pdf'
            : blob.type.includes('csv')
            ? 'csv'
            : null

          if (fileType) {
            const filename = `mitigation_plan_${docId}.${fileType}`

            const url = window.URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = filename
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            window.URL.revokeObjectURL(url)
          }

          setOpen(false)
          toast.success(t('mitigationPlanCreatedSuccessfully'))

          navigate({
            search: `?display=mitigationPlan`,
          })
        },
        onError: () => {
          setOpen(false)
          toast.error(t('errors.failedToCreateMitigationPlan'))
        },
      }
    )
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {!!recommendations.length && (
        <DialogTrigger asChild>
          <Button dataTestId="mitigation-modal-button">
            {t('createMitigationPlan')}
          </Button>
        </DialogTrigger>
      )}

      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t('mitigationPlan')}</DialogTitle>
          <DialogDescription>
            {t('mitigationPlanDescription')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <MitigationTable
            items={mitigationItems.items}
            selectedItemIds={mitigationItems.selectedItemIds}
            isAllSelected={mitigationItems.isAllSelected}
            onToggleSelection={mitigationItems.toggleSelection}
            onToggleSelectAll={mitigationItems.toggleSelectAll}
            onUpdateItem={mitigationItems.updateItem}
          />

          <Separator />

          <AddCustomRecommendation
            onAddCustomItem={mitigationItems.addCustomItem}
          />
        </div>

        <DialogFooter className="justify-between sm:justify-between items-center">
          <ExportActions
            exportOptions={exportOptions}
            selectedCase={caseSearch.selectedCase}
            onExportOptionChange={updateExportOption}
            onCaseSelect={caseSearch.setSelectedCase}
          />
          <Button
            onClick={handleCreateMitigation}
            dataTestId="submit-mitigation-plan"
            disabled={
              isCreatingMitigation ||
              mitigationItems.selectedItemIds.length === 0
            }
          >
            {isCreatingMitigation && (
              <Loader2Icon
                className="animate-spin"
                data-testid="loading-spinner"
                size={20}
              />
            )}
            {t('submit')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
