import { Checkbox, Label } from '@libs/ui'
import { t } from 'i18next'
import type { ExportOptions, SelectedCase } from '../types'
import { CaseSelector } from './CaseSelector'

interface ExportActionsProps {
  exportOptions: ExportOptions
  selectedCase: SelectedCase | null
  onExportOptionChange: (key: keyof ExportOptions, value: boolean) => void
  onCaseSelect: (case: SelectedCase | null) => void
}

export const ExportActions = ({
  exportOptions,
  selectedCase,
  onExportOptionChange,
  onCaseSelect,
}: ExportActionsProps) => {
  return (
    <div className="pt-4">
      <Label className="text-sm font-bold uppercase">
        {t('actions')}
      </Label>
      <div className="flex flex-wrap gap-4 mt-2">
        <div className="flex items-center gap-3">
          <Checkbox
            id="export-pdf"
            checked={exportOptions.exportToPdf}
            onCheckedChange={(checked) => onExportOptionChange('exportToPdf', !!checked)}
          />
          <Label htmlFor="export-pdf">{t('exportToPdf')}</Label>
        </div>

        <div className="flex items-center gap-3">
          <Checkbox
            id="export-csv"
            checked={exportOptions.exportToCsv}
            onCheckedChange={(checked) => onExportOptionChange('exportToCsv', !!checked)}
          />
          <Label htmlFor="export-csv">{t('exportToCsv')}</Label>
        </div>

        <div className="flex items-center gap-3">
          <Checkbox
            id="comment-jira"
            checked={exportOptions.commentInJira}
            onCheckedChange={(checked) => onExportOptionChange('commentInJira', !!checked)}
          />
          <Label htmlFor="comment-jira">{t('commentInJira')}</Label>
        </div>

        <div>
          <CaseSelector
            selectedCase={selectedCase}
            onCaseSelect={onCaseSelect}
            disabled={!exportOptions.commentInJira}
            className="w-[140px] justify-between"
            dataTestId="select-framework-button"
          />
        </div>
      </div>
    </div>
  )
}
