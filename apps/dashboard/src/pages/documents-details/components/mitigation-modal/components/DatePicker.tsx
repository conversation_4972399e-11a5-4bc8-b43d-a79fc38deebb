import { format } from 'date-fns'
import { ChevronDownIcon } from 'lucide-react'
import {
  Button,
  Calendar,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@libs/ui'
import { cn } from '@libs/common'

interface DatePickerProps {
  value: Date | null
  onChange: (date: Date | null) => void
  placeholder?: string
  className?: string
  dataTestId?: string
}

export const DatePicker = ({
  value,
  onChange,
  placeholder = 'Date',
  className,
  dataTestId,
}: DatePickerProps) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          dataTestId={dataTestId}
          variant="outline"
          className={cn(
            'w-full font-normal justify-between',
            !value && 'text-muted-foreground',
            className
          )}
        >
          {value ? format(value, 'PPP') : placeholder}
          <ChevronDownIcon className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={value}
          onSelect={(date: Date | undefined) => onChange(date || null)}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  )
}
