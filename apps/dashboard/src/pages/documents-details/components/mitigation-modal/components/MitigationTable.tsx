import {
  Checkbox,
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from '@libs/ui'
import { t } from 'i18next'
import type { MitigationPriority } from 'prime-front-service-client'
import type { MitigationItem } from '../types'
import { MitigationTableRow } from './MitigationTableRow'

interface MitigationTableProps {
  items: MitigationItem[]
  selectedItemIds: number[]
  isAllSelected: boolean
  onToggleSelection: (id: number) => void
  onToggleSelectAll: () => void
  onUpdateItem: (id: number, field: keyof MitigationItem, value: string | Date | MitigationPriority | null) => void
}

export const MitigationTable = ({
  items,
  selectedItemIds,
  isAllSelected,
  onToggleSelection,
  onToggleSelectAll,
  onUpdateItem,
}: MitigationTableProps) => {
  return (
    <Table>
      <TableHeader>
        <TableRow className="capitalize">
          <TableHead className="w-12">
            <Checkbox
              checked={isAllSelected}
              onCheckedChange={onToggleSelectAll}
            />
          </TableHead>
          <TableHead className="w-48">{t('title')}</TableHead>
          <TableHead>{t('description')}</TableHead>
          <TableHead className="w-40">{t('assignee')}</TableHead>
          <TableHead className="w-40">{t('dueDate')}</TableHead>
          <TableHead className="w-32">{t('priority')}</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {items.map((item) => (
          <MitigationTableRow
            key={item.id}
            item={item}
            isSelected={selectedItemIds.includes(item.id)}
            onToggleSelection={onToggleSelection}
            onUpdateItem={onUpdateItem}
          />
        ))}
      </TableBody>
    </Table>
  )
}
