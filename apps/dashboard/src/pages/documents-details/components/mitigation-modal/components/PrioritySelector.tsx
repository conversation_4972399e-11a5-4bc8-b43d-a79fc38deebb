import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@libs/ui'
import type { MitigationPriority } from 'prime-front-service-client'
import { PRIORITY_OPTIONS } from '../types'

interface PrioritySelectorProps {
  value: MitigationPriority | null
  onChange: (value: MitigationPriority) => void
  placeholder?: string
  className?: string
}

export const PrioritySelector = ({
  value,
  onChange,
  placeholder = 'Priority',
  className,
}: PrioritySelectorProps) => {
  return (
    <Select
      value={value || ''}
      onValueChange={(value) => onChange(value as MitigationPriority)}
    >
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {PRIORITY_OPTIONS.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}
