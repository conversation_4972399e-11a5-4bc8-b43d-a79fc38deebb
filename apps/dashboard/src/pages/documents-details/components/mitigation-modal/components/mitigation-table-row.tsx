import { Checkbox, Input, TableCell, TableRow } from '@libs/ui'
import type { MitigationPriority } from 'prime-front-service-client'
import type { MitigationItem } from '../types'
import { DatePicker, PrioritySelector } from './index'

interface MitigationTableRowProps {
  item: MitigationItem
  isSelected: boolean
  onToggleSelection: (id: number) => void
  onUpdateItem: (
    id: number,
    field: keyof MitigationItem,
    value: string | Date | MitigationPriority | null
  ) => void
}

export const MitigationTableRow = ({
  item,
  isSelected,
  onToggleSelection,
  onUpdateItem,
}: MitigationTableRowProps) => {
  return (
    <TableRow key={item.id}>
      <TableCell>
        <Checkbox
          checked={isSelected}
          onCheckedChange={() => onToggleSelection(item.id)}
        />
      </TableCell>
      <TableCell className="font-medium">{item.title}</TableCell>
      <TableCell className="text-sm text-muted-foreground">
        {item.description}
      </TableCell>
      <TableCell>
        <Input
          placeholder="Assignee"
          value={item.assignee || ''}
          onChange={(e) => onUpdateItem(item.id, 'assignee', e.target.value)}
        />
      </TableCell>
      <TableCell>
        <DatePicker
          value={item.due_date}
          onChange={(date) => onUpdateItem(item.id, 'due_date', date)}
          dataTestId="due-date-button"
        />
      </TableCell>
      <TableCell>
        <PrioritySelector
          value={item.priority}
          onChange={(priority) => onUpdateItem(item.id, 'priority', priority)}
          className="w-full"
        />
      </TableCell>
    </TableRow>
  )
}
