import { useState } from 'react'
import type { ExportOptions } from '../types'

export const useExportOptions = () => {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    exportToPdf: false,
    exportToCsv: false,
    commentInJira: false,
  })

  const updateExportOption = (key: keyof ExportOptions, value: boolean) => {
    setExportOptions((prev) => ({
      ...prev,
      [key]: value,
    }))
  }

  const setExportToPdf = (value: boolean) => updateExportOption('exportToPdf', value)
  const setExportToCsv = (value: boolean) => updateExportOption('exportToCsv', value)
  const setCommentInJira = (value: boolean) => updateExportOption('commentInJira', value)

  return {
    exportOptions,
    setExportToPdf,
    setExportToCsv,
    setCommentInJira,
    updateExportOption,
  }
}
