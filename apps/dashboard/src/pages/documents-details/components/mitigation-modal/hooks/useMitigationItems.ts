import { useCallback, useEffect, useState } from 'react'
import type { DesignDocTopPolicyRecommendation, MitigationPriority } from 'prime-front-service-client'
import type { MitigationItem } from '../types'

interface UseMitigationItemsProps {
  recommendations: DesignDocTopPolicyRecommendation[]
}

export const useMitigationItems = ({ recommendations }: UseMitigationItemsProps) => {
  const [items, setItems] = useState<MitigationItem[]>([])
  const [selectedItemIds, setSelectedItemIds] = useState<number[]>([])

  // Initialize items from recommendations
  useEffect(() => {
    setItems(
      recommendations.map((recommendation) => ({
        id: recommendation.id,
        title: recommendation.title,
        description: recommendation.description,
        assignee: null,
        due_date: null,
        priority: null,
        selected: false,
      }))
    )
  }, [recommendations])

  const updateItem = useCallback(
    (id: number, field: keyof MitigationItem, value: string | Date | MitigationPriority | null) => {
      setItems((prevItems) =>
        prevItems.map((item) =>
          item.id === id ? { ...item, [field]: value } : item
        )
      )
    },
    []
  )

  const toggleSelection = useCallback((id: number) => {
    setSelectedItemIds((prev) =>
      prev.includes(id) ? prev.filter((i) => i !== id) : [...prev, id]
    )
  }, [])

  const toggleSelectAll = useCallback(() => {
    setSelectedItemIds((prev) =>
      prev.length === items.length ? [] : items.map((item) => item.id)
    )
  }, [items])

  const addCustomItem = useCallback((newItem: Omit<MitigationItem, 'id' | 'selected'>) => {
    const newId = Math.max(...items.map((item) => item.id), 0) + 1
    const itemToAdd: MitigationItem = {
      ...newItem,
      id: newId,
      selected: false,
    }
    setItems((prev) => [...prev, itemToAdd])
  }, [items])

  const getSelectedItems = useCallback(() => {
    return items.filter((item) => selectedItemIds.includes(item.id))
  }, [items, selectedItemIds])

  return {
    items,
    selectedItemIds,
    updateItem,
    toggleSelection,
    toggleSelectAll,
    addCustomItem,
    getSelectedItems,
    isAllSelected: selectedItemIds.length === items.length,
    hasSelectedItems: selectedItemIds.length > 0,
  }
}
