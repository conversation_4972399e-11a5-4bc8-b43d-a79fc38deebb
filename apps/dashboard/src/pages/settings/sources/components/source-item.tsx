import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogTrigger,
  JiraIcn,
} from '@libs/ui'
import { Pencil, RefreshCwIcon } from 'lucide-react'
import type { SourceModelResponse } from 'prime-front-service-client'
import type { SourceInfo } from '../utils'
import { toast } from 'sonner'
import { cn, extractJiraSubdomainName } from '@libs/common'
import { errorsMap, useFetchJiraSource } from '../../../../api/use-sources-api'
import type { ResponseError } from 'prime-front-service-client'
import { t } from 'i18next'
import { UpdateSourceForm } from './update-source-form'
import { sourceModalAtom } from '../page'
import { useAtom } from 'jotai'

interface SourcesTableItemProps {
  source: SourceModelResponse
}

export const SourceItem = ({ source }: SourcesTableItemProps) => {
  const [open, setOpen] = useAtom(sourceModalAtom)
  const mutation = useFetchJiraSource()
  const sourceInfo = source?.info as SourceInfo

  const handleFetchSource = async () => {
    mutation.mutate(source.id, {
      onSuccess: (data) => {
        toast.success(`Source status: ${data.status}`)
      },
      onError: async (error: Error) => {
        const responseError = error as ResponseError
        const message = await responseError?.response?.json()
        if (message?.exception === errorsMap.fetchAlreadyRunning) {
          toast.error(t('errors.fetchAlreadyRunning'))
        } else {
          toast.error('Failed to fetch source')
        }
      },
    })
  }

  return (
    <div
      key={source.id}
      className="w-full px-6 py-4 bg-white rounded-xl shadow border border-gray-200 justify-between items-center inline-flex"
    >
      <div className="flex-col justify-start items-start gap-2 inline-flex">
        <div className="rounded-md justify-start items-start gap-2 inline-flex">
          <JiraIcn className="mt-2" />
          <div className="flex-col justify-start items-start gap-1 inline-flex ml-2">
            <div className="text-gray-600 text-xl font-bold">
              {sourceInfo?.name ||
                extractJiraSubdomainName(sourceInfo?.jira_url || '')}
            </div>
            <div>
              <span className="text-gray-600 text-xs font-normal">
                {source?.source_type} -{' '}
              </span>
              <a
                className="text-gray-600 text-xs underline underline-offset-4"
                href={sourceInfo?.jira_url}
                target="_blank"
                rel="noreferrer"
              >
                {sourceInfo?.jira_url}
              </a>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button variant="ghost" dataTestId="edit-jira-source">
              <Pencil />
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="text-xl">
                {t('add')} {t('source')}
              </DialogTitle>
            </DialogHeader>
            <UpdateSourceForm sourceId={source.id} />
          </DialogContent>
        </Dialog>
        <Button
          onClick={() => handleFetchSource()}
          variant="outline"
          className="justify-center items-center gap-1.5 flex"
          disabled={mutation.isPending}
          dataTestId="fetch-button"
        >
          <RefreshCwIcon
            className={cn(
              'w-5 h-5 relative',
              mutation.isPending ? 'animate-spin' : 'animate-none'
            )}
          />
          <span className="text-xs">{t('fetch')}</span>
        </Button>
      </div>
    </div>
  )
}
