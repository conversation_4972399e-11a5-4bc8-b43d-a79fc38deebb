import type { FilterItem } from '@libs/ui'
import {
  Button,
  Checkbox,
  ClipLoader,
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@libs/ui'
import { t } from 'i18next'
import { useEffect, useState } from 'react'
import { useGetCasesSearchAutocomplete } from '../../../api/use-cases-api'
import { ChevronDown } from 'lucide-react'
import type { ProviderFieldInfoOptions } from 'prime-front-service-client'

interface MultiSelectFilterProps {
  id: string
  fields: ProviderFieldInfoOptions[]
  filterOperator: string
  savedFilter?: FilterItem
  onFilterChange: (filter: FilterItem) => void
  withSearch?: boolean
}

export function MultiSelectFilter({
  id,
  fields,
  savedFilter,
  filterOperator,
  onFilterChange,
  withSearch = false,
}: MultiSelectFilterProps) {
  const [options, setOptions] = useState<string[]>([])
  const [, setInitialOptions] = useState<string[]>(options)
  const [isOpenCombo, setIsOpenCombo] = useState(false)
  const [selectedOptions, setSelectedOptions] = useState(
    savedFilter?.value || []
  )

  const [searchQuery, setSearchQuery] = useState('')
  const {
    refetch,
    data: searchData,
    isLoading,
  } = useGetCasesSearchAutocomplete({
    value: searchQuery,
    field: id,
  })

  useEffect(() => {
    const field = fields.find((f) => f.id === id)
    if (field) {
      setOptions(field?.options || [])
    }
  }, [id, fields])

  const onSelect = (option: string) => {
    setSelectedOptions((prev: any) => {
      const isSelected = prev.includes(option)
      const newSelectedOptions = isSelected
        ? prev.filter((selected: string) => selected !== option)
        : [...prev, option]

      onFilterChange({
        field: id,
        op: filterOperator,
        value: newSelectedOptions,
      })

      return newSelectedOptions
    })
  }

  useEffect(() => {
    if (searchQuery) {
      refetch()
    }
  }, [refetch, searchQuery])

  useEffect(() => {
    if (savedFilter) {
      const { value } = savedFilter
      setSelectedOptions(Array.isArray(value) ? value : [value])
      setInitialOptions((prevOptions) => {
        const newOptions = new Set(prevOptions)

        if (Array.isArray(value)) {
          value.forEach((val) => newOptions.add(val))
        } else {
          newOptions.add(value)
        }
        return Array.from(newOptions)
      })
    } else {
      setSelectedOptions([])
    }
  }, [savedFilter])

  const searchResults = searchData?.options || []

  return (
    <Popover>
      <PopoverTrigger className="w-44 capitalize flex items-center justify-between rounded-full bg-background px-4 py-2 text-sm font-medium border border-gray-200 h-10">
        {t('select')}
        <ChevronDown className="h-4 w-4" />
      </PopoverTrigger>
      <PopoverContent>
        <div>
          {withSearch && (
            <div className="">
              <Popover open={isOpenCombo} onOpenChange={setIsOpenCombo}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    dataTestId="search-combo"
                    className="w-full justify-start text-slate-400 border-slate-400 mb-4"
                  >
                    {t('search')}...
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[200px] p-0" align="start">
                  <Command>
                    <CommandInput
                      placeholder="Search..."
                      value={searchQuery}
                      onValueChange={setSearchQuery}
                    />
                    <CommandList>
                      <CommandEmpty>
                        {isLoading ? (
                          <ClipLoader />
                        ) : searchQuery === '' ? (
                          <span className="text-xs">
                            Start typing to load results
                          </span>
                        ) : (
                          'No results found.'
                        )}
                      </CommandEmpty>
                      <CommandGroup>
                        {searchResults.map((option) => (
                          <CommandItem
                            key={crypto.randomUUID()}
                            value={option}
                            onSelect={(value) => {
                              setInitialOptions((prev) => {
                                if (!prev.includes(value)) {
                                  return [...prev, value]
                                }
                                return prev
                              })

                              onSelect(value)
                              setIsOpenCombo(false)
                            }}
                          >
                            {option}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>
          )}

          <Command>
            <CommandList>
              <CommandEmpty>{t('noResultsFound')}</CommandEmpty>
              <CommandGroup className=" max-h-64 overflow-auto">
                <div>
                  {options?.map((option) => {
                    const isSelected = selectedOptions.includes(option)
                    return (
                      <CommandItem
                        key={crypto.randomUUID()}
                        onSelect={() => onSelect(option)}
                        className="gap-2"
                      >
                        <Checkbox checked={isSelected} />

                        {option}
                      </CommandItem>
                    )
                  })}
                </div>
              </CommandGroup>
            </CommandList>
          </Command>
        </div>
      </PopoverContent>
    </Popover>
  )
}
