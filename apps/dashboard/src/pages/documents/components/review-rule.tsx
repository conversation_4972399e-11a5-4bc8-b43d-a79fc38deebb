import {
  <PERSON><PERSON>,
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
  TableSkeleton,
} from '@libs/ui'
import type { FilterItem } from '@libs/ui'
import { t } from 'i18next'
import { useGetCasesForAccount } from '../../../api/use-cases-api'
import React from 'react'

interface ReviewRuleProps {
  ruleName: string
  projects: string[]
  filters: FilterItem[]
  addRule: () => void
  back: () => void
}

export const ReviewRule = ({
  ruleName,
  projects,
  filters,
  addRule,
  back,
}: ReviewRuleProps) => {
  const { data, isPending } = useGetCasesForAccount({
    limit: 10,
    offset: 0,
    filters: [
      ...filters,
      {
        field: 'provider_fields.project',
        op: 'eq',
        value: [...projects],
      },
    ].map((filter) => JSON.stringify(filter)),
  })

  return (
    <div className="flex flex-col gap-4 p-4 ">
      <div>
        <h2 className="text-lg font-semibold">{ruleName}</h2>
      </div>

      <div className="bg-white h-[600px] overflow-auto">
        {isPending ? (
          <TableSkeleton />
        ) : !data?.results?.length ? (
          <div>
            <div className="text-center">
              <div className="div">{t('noResultsFound')}</div>
              <div className="mb-2">{t('tryDifferentFilter')}</div>
            </div>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow className="capitalize">
                <TableHead className="px-4 py-6 w-72">{t('title')}</TableHead>
                <TableHead className="px-4 py-6">{t('ticketId')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.results?.map((activity) => (
                <TableRow key={crypto.randomUUID()}>
                  <TableCell className="font-medium p-4 text-muted-foreground capitalize">
                    {activity.title}
                  </TableCell>
                  <TableCell className="font-medium p-4 text-muted-foreground capitalize">
                    {activity.issue_id}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>
      <div className="flex justify-end mt-4 gap-2">
        <Button
          className="capitalize"
          dataTestId="back-button"
          variant="secondary"
          onClick={back}
        >
          {t('back')}
        </Button>
        <Button onClick={addRule} dataTestId="save-rule" className="capitalize">
          {t('apply')}
        </Button>
      </div>
    </div>
  )
}
