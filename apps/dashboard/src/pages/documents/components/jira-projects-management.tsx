import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@libs/ui'
import { t } from 'i18next'
import { SettingsIcon } from 'lucide-react'
import { useGetAllSources } from '../../../api/use-sources-api'
import { sourcesApi } from '../../../api/clients'
import { useSourceForm } from '../../settings/sources/hooks/use-source-form'
import { useEffect, useMemo, useState } from 'react'
import type {
  JiraInsertModel,
  ProjectResponse,
  SourceModelResponse,
} from 'prime-front-service-client'
import { useInfiniteQuery } from '@tanstack/react-query'
import { SourceSettings } from '../../settings/sources/components/source-settings'

interface JiraProjectsManagementProps {
  buttonText?: string
  buttonIcon?: React.ReactNode
}

const LIMIT = 100

export const JiraProjectsManagement = ({
  buttonText,
  buttonIcon,
}: JiraProjectsManagementProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const { data: sources, isLoading: isLoadingSources } = useGetAllSources(1)
  const [currentSource, setCurrentSource] =
    useState<SourceModelResponse | null>(null)

  const {
    data: jiraProjects,
    isPending: isPendingJiraProjects,
    isFetching: isFetchingJiraProjects,
    isLoading: isLoadingJiraProjects,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery({
    queryKey: ['jiraProjects', currentSource?.id],
    queryFn: async (params) => {
      if (!currentSource?.id) {
        return undefined
      }

      return await sourcesApi.getJiraProjectsBySourceId({
        source_id: currentSource?.id || 0,
        limit: LIMIT,
        offset: params.pageParam * LIMIT,
        check_access: true,
      })
    },
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage || !lastPage.has_next) {
        return undefined
      }
      return allPages.length
    },
    initialPageParam: 0,
    enabled: !!currentSource?.id,
    refetchOnWindowFocus: false,
  })

  const flatJiraProjects: ProjectResponse[] = useMemo(() => {
    if (!jiraProjects?.pages) {
      return [] as ProjectResponse[]
    }
    return (
      jiraProjects?.pages
        ?.flatMap((page) => page?.results ?? [])
        .filter(
          (project): project is ProjectResponse => project !== undefined
        ) ?? []
    )
  }, [jiraProjects?.pages])

  const {
    editBaseForm,
    editSourceSettingsForm,
    projectsOptions,
    handleEditFormSubmit,
    updateSourceMutation,
    selectedProjects,
    handleProjectChange,
    setProjectsOptions,
    setSelectedProjects,
    source,
  } = useSourceForm(currentSource?.id || 0)

  useEffect(() => {
    if (sources && sources.length > 0) {
      const jiraSources = sources.filter(
        (source) => source.source_type === 'Jira'
      )

      if (jiraSources.length === 0) {
        return
      }

      const jiraSource = jiraSources[0]
      if (jiraSource) {
        setCurrentSource(jiraSource)
      }
    }
  }, [sources])

  useEffect(() => {
    if (hasNextPage && !isFetchingNextPage && !isPendingJiraProjects) {
      fetchNextPage()
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage, isPendingJiraProjects])

  useEffect(() => {
    setProjectsOptions(() => [...flatJiraProjects])
  }, [flatJiraProjects, setProjectsOptions])

  useEffect(() => {
    const sourceInfo = source?.info as JiraInsertModel
    if (sourceInfo) {
      editBaseForm.reset({
        name: sourceInfo.name || '',
        jira_url: sourceInfo.jira_url || '',
        email: sourceInfo.email || '',
        api_token: sourceInfo.api_token || '',
      })

      // TODO: fix tsc build
      // setSelectedProjects(sourceInfo.design_review_projects || [])

      if (sourceInfo.jql_parameters_filter) {
        editSourceSettingsForm.reset({
          // TODO: fix tsc build
          // projects: sourceInfo.design_review_projects || [],
          since_in_days: sourceInfo.jql_parameters_filter.since_in_days,
        })
      }
    }
  }, [editBaseForm, editSourceSettingsForm, setSelectedProjects, source])

  const onSubmit = async () => {
    try {
      await handleEditFormSubmit()
      setIsOpen(false)
    } catch (error) {
      console.error('Error submitting form:', error)
    }
  }

  const isLoading =
    isPendingJiraProjects ||
    isFetchingJiraProjects ||
    isLoadingJiraProjects ||
    isFetchingNextPage ||
    isLoadingSources ||
    hasNextPage

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          dataTestId="manage-projects-button"
          className="flex items-center gap-2 capitalize"
          variant="outline"
        >
          {buttonIcon ? buttonIcon : <SettingsIcon className="w-4 h-4" />}

          {buttonText || t('manageProjects')}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold capitalize">
            {t('jiraProjectsManagement')}
          </DialogTitle>
          <DialogDescription className="text-sm text-muted-foreground">
            {t('jiraProjectsManagementDescription')}
          </DialogDescription>
        </DialogHeader>
        {isLoading ? (
          <div className="flex justify-center items-center min-h-96">
            <ClipLoader />
          </div>
        ) : (
          currentSource?.id && (
            <SourceSettings
              baseForm={editBaseForm}
              sourceSettingsForm={editSourceSettingsForm}
              projectsOptions={projectsOptions}
              updatePending={updateSourceMutation.isPending}
              selectedProjects={selectedProjects}
              showNumberOfDays={false}
              handleProjectChange={handleProjectChange}
              onSubmit={onSubmit}
              onCancel={() => setIsOpen(false)}
              loadMoreProjects={fetchNextPage}
              isFetchingMore={isFetchingNextPage}
              hasMoreProjects={hasNextPage}
            />
          )
        )}
      </DialogContent>
    </Dialog>
  )
}
