import { useMutation, useQuery } from '@tanstack/react-query'
import { mitigationsApi } from './clients'
import { toast } from 'sonner'
import type { CreateMitigationsOperationRequest } from 'prime-front-service-client'
import { apiConfig } from '@libs/common'

const defaultQueryConfig = {
  refetchOnWindowFocus: false,
  retry: false,
}

// get all mitigations
export const useGetMitigations = (security_review_id: number) =>
  useQuery({
    queryKey: ['getMitigations', security_review_id],
    queryFn: async () => {
      if (!security_review_id) {
        console.log('No security review id')
        return []
      }
      try {
        return await mitigationsApi.getMitigations({
          security_review_id,
        })
      } catch (error) {
        toast.error('Failed to fetch mitigations')
        throw new Error('Failed to fetch mitigations')
      }
    },
    ...defaultQueryConfig,
  })

// create mitigations
export const useCreateMitigation = () =>
  useMutation({
    mutationKey: ['createMitigation'],
    mutationFn: async (mitigation: CreateMitigationsOperationRequest) => {
      const response = await fetch(
        `${apiConfig.basePath}/security-reviews/${mitigation.security_review_id}/mitigations`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(mitigation.CreateMitigationsRequest),
          credentials: 'include',
        }
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return response
    },
  })
