{"dashboard": {"topReviewsTitle": "top reviews pending", "title": "Dashboard", "productsByRiskCategory": "Products by Risk Category", "productsCategory": "products category", "noPendingReviews": "No pending reviews", "riskProbabilityTitle": "your risk probability profile", "epicsTitle": "Top 10 Containers by Risk Score", "containersTitle": "Top 10 Containers by Risk Score"}, "login": {"login": "<PERSON><PERSON>", "loginWithPassword": "Login with Password", "loginWith": "Login with", "loginPolicy": "By logging in, you acknowledge that you have read and agree to the Prime", "privacyPolicy": "Privacy Policy."}, "workroomPage": {"issueIDLabel": "Issue ID", "riskReviewCompleted": "Risk Review Completed", "importedFromJira": "Imported from Jira", "ticketSummary": "Ticket Summary", "mainKeywords": "Main Keywords", "assessmentTitle": "Why was this issue flagged", "dismissAll": "dismiss all", "reviewAndConfirmRecommendations": "Review & Confirm Recommendations", "completeReview": "Complete Review", "writeToJira": "Write to <PERSON><PERSON>", "confirmRecommendations": "Confirm Recommendations", "leaveABriefCommentOnWhatWasDone": "Leave a brief comment on what was done", "confirmSelected": "Confirm Selected", "reclassify": "Reclassify", "dismissReview": "dismiss review", "companyPolicy": "company policy", "industryPolicy": "Industry Policy", "setComment": "Set Comment", "leavePageDialogTitle": "Leaving page will lead to losing progress", "leavePageDialogDescription": "Your progress won't be be saved", "leavePageConfirm": "Leave Anyway", "leavePageCancel": "Stay on Page", "confirmConcerns": "Confirm concerns", "action": "action", "activityLog": "Activity Log", "activity": "activity", "author": "author", "date": "date", "post": "post", "dialogTitle": "Share the mitigation plan with your team", "caseStatusConfidence": "Case Status Confidence", "caseStatusInPrime": "Case Status in Prime", "measureOfCertainty": "Measure of Certainty", "indicatesTheLevelOfPotentialRisk": "Indicates the level of potential risk", "jiraTicketID": "Jira Ticket ID", "labels": "Labels"}, "summariesPage": {"issueIDLabel": "Issue ID", "riskReviewCompleted": "Risk Review Completed", "importedFromJira": "Imported from Jira", "ticketSummary": "Ticket Summary", "mainKeywords": "Main Keywords", "assessmentTitle": "Why was this issue flagged", "dismissAll": "dismiss all", "reviewAndConfirmRecommendations": "Review & Confirm Recommendations", "completeReview": "Complete Review", "writeToJira": "Write to <PERSON><PERSON>", "confirmRecommendations": "Confirm Recommendations", "leaveABriefCommentOnWhatWasDone": "Leave a brief comment on what was done", "confirmSelected": "Confirm Selected", "reclassify": "Reclassify", "dismissReview": "dismiss review", "companyPolicy": "company policy", "industryPolicy": "Industry Policy", "setComment": "Set Comment", "leavePageDialogTitle": "Leaving page will lead to losing progress", "leavePageDialogDescription": "Your progress won't be be saved", "leavePageConfirm": "Leave Anyway", "leavePageCancel": "Stay on Page", "confirmConcerns": "Confirm concerns", "action": "action", "activityLog": "Activity Log", "activity": "activity", "author": "author", "date": "date", "post": "post", "dialogTitle": "Share the mitigation plan with your team", "measureOfCertainty": "Measure of Certainty", "indicatesTheLevelOfPotentialRisk": "Indicates the level of potential risk", "jiraTicketID": "Jira Ticket ID", "labels": "Labels", "project": "Project"}, "activityLog": {"activityLog": "Activity Log", "riskCategoryWasOverridden": "Risk category was overridden", "statusWasUpdated": "Status was updated", "userViewCase": "User view case"}, "reopenCase": "Reopen Case", "eq": "Equals", "ne": "Not Equals", "type": "Type...", "recommendationsTitle": "Review Concerns & Confirm Recommendations", "importedFields": "Imported Fields", "imported": "imported", "reviewed": "reviewed", "workroom": "Workroom", "edit": "Edit", "cancel": "Cancel", "submit": "Submit", "save": "Save", "search": "Search", "close": "Close", "email": "Email", "settings": "Settings", "home": "Home", "assetsMap": "Assets Map", "sources": "Sources", "configuration": "Configuration", "tasksStatus": "Tasks Status", "noTasksFound": "No tasks found", "identified": "Identified", "completed": "Completed", "new": "New", "scanned": "Scanned", "error": "Error", "continue": "Continue", "comments": "Comments", "uploadJSONFile": "Upload JSON File", "uploadJSONDescription": "Upload a JSON file and store it in your browser's local storage", "onlyJSONAllowed": "Only JSON files are allowed", "jiraURL": "Jira URL", "addSource": "Add Source", "noSources": "No sources yet", "analysis": "Analysis", "intervene": "Intervene", "analyze": "Analyze", "monitor": "Monitor", "allReviews": "All Reviews", "somethingWentWrong": "Something went wrong", "creationDate": "Creation Date", "assignee": "Assignee", "status": "Status", "owner": "Owner", "confidence": "Confidence", "riskFactors": "Risk Factors", "riskInCategories": "Risk in Categories", "availability": "availability", "compliance": "compliance", "confidentiality": "confidentiality", "integrity": "integrity", "thirdParty": "3rd Party", "pageNotFound": "Page not found", "goBackHome": "Go back home", "back": "back", "pageNotFoundDescription": "Sorry, we couldn't find the page you're looking for. Please check the URL or try again later.", "today": "Today", "lastWeek": "Last Week", "lastMonth": "Last Month", "total": "Total", "fetchSource": "Fetch Source", "saveSource": "Save Source", "source": "source", "copySuccess": "Copied to clipboard", "sourceForm": {"name": "Source Title", "api_token": "API Token", "jira_url": "Jira URL", "email": "Email", "jql": "JQL"}, "fullName": "full name", "copy": "copy", "delete": "Delete", "uploader": "uploader", "areYouAbsolutelySure": "Are you absolutely sure", "thisActionCannotBeUndone": "This action cannot be undone", "thisWillPermanentlyDeleteSource": "This will permanently delete the source", "thisWillPermanentlyDeleteNotification": "This will permanently delete your notification", "jiraAttributes": "jira attributes", "youAreOffline": "You are offline", "youAreOnline": "You are online", "failedToGetAccountDetails": "Failed to get account details", "jql": "JQL", "noData": "no data", "fileHasBeenSavedToIndexedDB": "File has been saved to indexedDB", "sourceAddedSuccessfully": "Source added successfully", "sourceDeletedSuccessfully": "Source deleted successfully", "sourceUpdatedSuccessfully": "Source updated successfully", "errorParsingJSONFile": "Error parsing JSON file", "caseCompletedSuccessfully": "Case completed Successfully", "commentUpdatedSuccessfully": "Comment updated successfully", "updatedRecommendationsSuccessfully": "Updated recommendations successfully", "addedCustomRecommendationSuccessfully": "Added custom recommendation successfully", "areYouSure": "Are you sure?", "logoutActionMessage": "This action will log you out of the application", "logout": "Logout", "backTo": "back to", "add": "Add", "user": "user", "view": "view", "views": "Views", "createdAt": "Created At", "manageSourcesForSecurityReviews": "Manage sources for security reviews", "sourceDeleteConfirmWarning": "Any data associated with this source will be permanently deleted", "youCannotUndoThisAction": "You cannot undo this action", "updatedJiraAttributesSuccessfully": "Updated jira attributes successfully", "usersManagement": "Users Management", "manageUsersAndTheirRolesInTheApplication": "Manage users and their roles in the application", "addTeamMembers": "Add Team Members", "inviteByEmail": "Invite by <PERSON><PERSON>", "enterEmail": "Enter email", "enterFirstName": "enter first name", "enterLastName": "enter last name", "invite": "invite", "dismiss": "dismiss", "undismiss": "undismiss", "pending": "pending", "userDeletedSuccessfully": "User deleted successfully", "fetchingMore": "Fetching More", "loadingMore": "Loading more", "loadNewer": "<PERSON><PERSON>", "nothingMoreToLoad": "Nothing more to load", "nothingToShow": "Nothing to show", "tryDifferentFilter": "Perhaps try a different filter", "last": "last", "day": "day", "sevenDays": "7 days", "fourteenDays": "14 days", "thirtyDays": "30 days", "trendsTitle": "Security Reviews For", "noApprovedConcerns": "no approved concerns", "fetch": "<PERSON>tch", "process": "Process", "configureMfaTitle": "Set up Two-Factor Authentication", "configureMfaDescription": "In your 2FA-compatible app, please scan the following QR code:", "configureMfaCode": "After the application is configured, please enter the authentication code", "mfaTitle": "Two-Factor Authentication", "mfaDescription": "Please enter the code generated with your authenticator app", "userName": "user name", "summary": "summary", "detailed": "detailed", "scannedIn": "Scanned in", "apply": "apply", "clear": "clear", "clearFilters": "Clear Filters", "applySaveToView": "Apply & Save to View", "auditLogs": "<PERSON><PERSON>", "user_login": "user login", "user_logout": "user logout", "user_password_change": "user password change", "user_password_reset": "user password reset", "add_user": "add user", "remove_user": "remove user", "add_source": "add source", "remove_source": "remove source", "deleting": "deleting", "pleaseTryAgainLater": "Please try again later", "goToHome": "Go to Home", "goToLogin": "Go to Login", "reopen": "reopen", "reopenConfirm": "Are you sure you would like to reopen the review?", "caseReopenedSuccessfully": "Case reopened Successfully", "defaultView": "Default View", "epicsView": "Epics View", "viewTitle": "View Title", "filtersSavedSuccessfully": "Filters saved successfully", "addAView": "Add a view", "setUpShortcut": "Set up a shortcut", "saveChanges": "Save Changes", "selectAView": "Select a view", "totalCases": "Total Cases", "selectAWorkroomViewForTheShortcut": "Select a workroom view for the shortcut", "presetDeletedSuccessfully": "Preset deleted successfully", "presetCreatedSuccessfully": "Preset created successfully", "whyWasItDismissed": "Why was it dismissed?", "missingContext": "Missing Context", "notAnIssue": "Not an Issue", "riskScoreCategoryUpdatedSuccessfully": "Risk score category updated successfully", "statusUpdatedSuccessfully": "Status updated successfully", "saveToView": "Save to view", "numberOfDaysToPullDataBasedOn": "Number of days to pull data based on", "confirmJQLMessage": "I confirm that I understand that direct JQL interaction can cause issues with the system's usability", "noResultsFound": "No results found", "securityFrameworks": "Security Frameworks", "securityFrameworksDescription": "Pick the security framework to use for security reviews and recommendations", "updatedSecurityFrameworkSuccessfully": "Updated security framework successfully", "securityFrameworkConfirmation": "Change of framework will lead to change of results in recommendation. Are you willing to continue?", "securityFrameworkConfirm": "Yes, change framework", "securityFrameworkCancel": "No, Keep as is", "selectDaysNumber": "Select days number", "selectDateRange": "Select date range", "notEquals": "not equals", "equals": "equals", "notification": "Notification", "notifications": "notifications", "when?": "When?", "customRecommendationsTitle": "Custom Recommendations", "customRecommendationsDescription": "Edit and delete custom recommendations that created by users", "recommendation": "recommendation", "ticket": "ticket", "control": "control", "editedBy": "edited by", "edited": "edited", "created": "created", "create": "create", "searchLabels": "Search labels...", "addLabel": "Add Label", "slack": "<PERSON><PERSON>ck", "connected": "Connected", "notConnected": "Not connected", "connectSlackAccount": "Connect Slack Account", "disconnectSlackAccount": "Disconnect <PERSON><PERSON><PERSON> Account", "slackDisconnected": "Slack Disconnected", "slackDisconnectError": "An error occurred while disconnecting from Slack", "slackDisconnectConfirm": "Are you sure to disconnect the Slack account?", "slackDisconnectDescription": "You could reconnect it anytime.", "disconnect": "Disconnect", "time": "time", "channel": "channel", "tryToReloadThePage": "Try to reload the page", "slackDescription": "Keep your notifications up-to-date in your favorite channels", "notificationSettingsUpdated": "Notification settings updated", "authentication": "Authentication", "setUpLoginPreferences": "Set up login preferences", "addSamlLogin": "Add SAML Login", "editSamlLogin": "Edit SAML Login", "savedSamlLoginSuccessfully": "SAML login successfully saved", "addedSamlLoginSuccessfully": "SAML login successfully added", "singleSignOn": "Single sign on URL", "issuer": "Issuer", "certificate": "Certificate", "entityID": "Entity ID", "acsURL": "Assertion consumer service (ACS) URL", "noChannelFound": "No channel found", "searchChannel": "Search channel...", "noUsersFound": "No users found", "recommendationsUpdatedSuccessfully": "Recommendations updated successfully", "recommendationDeletedSuccessfully": "Recommendation deleted successfully", "deleteRecommendation": "Delete Recommendation", "addRecommendation": "add recommendation", "emptyRecommendationsMessage": "There are no recommendations in this framework.", "changeFrameworkMessage": "Try changing to another framework.", "changeFramework": "change framework", "errors": {"invalidEmail": "Invalid email", "failedToIdentifyUser": "Failed to identify user", "incorrectUserOrPassword": "Incorrect username or password", "failedToFetchCases": "Failed to fetch cases", "failedToFetchCasesForSource": "Failed to fetch cases for source", "failedToFetchCaseDetails": "Failed to fetch case details", "failedToFetchIsServicesAlive": "Failed to fetch isServicesAlive", "failedToFetchIsAlive": "Failed to fetch isAlive", "failedToFetchTasks": "Failed to fetch tasks", "failedToGetTaskById": "Failed to get task by id", "failedToFetchSources": "Failed to fetch sources", "failedToFetchSource": "Failed to fetch source", "failedToFetchAssetsMap": "Failed to fetch assets map", "failedToAddSource": "Failed to add source", "failedToDeleteSource": "Failed to delete source", "failedToUpdateSource": "Failed to update source", "failedToDeleteUser": "Failed to delete user", "commentUpdateFailed": "Comment update failed", "completingCaseFailed": "Completing case failed", "reopeningCaseFailed": "Reopening case failed", "updatingRecommendationsFailed": "Updating recommendations failed", "updatingConfigFailed": "Updating config failed", "errorFetchingData": "Error fetching data", "incorrectMfaCode": "Invalid code", "anUnexpectedError": "An unexpected error has occurred", "failedToSaveFilters": "Failed to save filters", "failedToUpdateRiskScoreCategory": "Failed to update risk score category", "failedToUpdateStatus": "Failed to update status", "failedToDeletePreset": "Failed to delete preset", "days": "days", "failedToFetchProjects": "Failed to fetch projects", "failedToCreatePreset": "Failed to create preset", "failedToFetchSelectedFramework": "Failed to fetch selected security framework", "updatingSecurityFrameworkFailed": "Failed to update security framework", "failedToUpdateNotificationSettings": "Failed to update notification settings", "failedSavingSamlLogin": "Failed saving SAML login", "failedToDeletedRecommendation": "Failed to delete recommendation", "addingCustomRecommendationFailed": "Failed adding custom recommendation", "emailAndPasswordRequired": "Email and password are required", "failedToUpdateViolation": "Failed to update Prime security violation", "bulkUpdateFailed": "Failed updating cases", "fetchAlreadyRunning": "Source process is already running", "importingRecommendationsFailed": "Failed to import recommendations", "csvInvalidFieldsError": "Invalid <PERSON>. Please check your file and try again.", "invalidCSVFormat": "Invalid CSV format", "attributesRefreshFailed": "Refresh failed", "addWatcherFailed": "Failed adding watcher", "failedToFetchSummaries": "Failed to fetch summaries", "recommendationsGenerationFailed": "Failed to generate recommendations", "reviewStartFailed": "Failed generating review", "generatingReviewAlreadyRunning": "A new design review is underway. You can follow its progress through the task status updates.", "failedToUploadLink": "Link upload failed", "failedToUploadDocuments": "Failed to upload documents", "failedToUploadFiles": "Failed to upload files", "designReviewAlreadyExist": "Design review already exists", "failedToCreateMitigationPlan": "Failed to create mitigation plan"}, "resetPasswordTitle": "Reset Password for", "passwordRequirements": {"validLength": "At least 8 characters long", "hasNumber": "Must contain at least 1 number", "hasUpperCase": "Must contain at least 1 uppercase letter", "hasLowerCase": "Must contain at least 1 lowercase letter", "hasSpecialCharacter": "Must contain at least 1 special character or non-leading/trailing space", "passwordsMatch": "Passwords do not match"}, "tableColumns": {"title": "title", "status": "Status Review", "state": "state", "confidence": "confidence", "confidentiality": "confidentiality", "integrity": "integrity", "availability": "availability", "compliance": "compliance", "thirdPartyManagement": "3rd Party", "creator": "creator", "createdDate": "created date", "riskFactors": "risk factors", "issueOwner": "issue owner", "issueDate": "issue date", "riskScoreCategory": "risk score category", "riskScore": "risk score", "confidence_level": "confidence level", "risk_score_category": "risk score category", "confidentiality_level": "confidentiality level", "integrity_level": "integrity level", "availability_level": "availability level", "issueId": "issue id", "issue_id": "issue id", "labels": "labels", "detected": "detected", "project": "project", "reporter": "reporter", "created": "created", "type": "type", "issue_name": "issue name", "risk_score": "risk score", "epicTitle": "epic title", "containerTitle": "container title", "epicRiskScore": "epic risk score", "linddun_categories": "LINDDUN", "mitre_categories": "MITRE ATT&CK", "fire_summary": "Prime Abstract", "provider_fields.project": "project", "provider_fields.created": "created", "provider_fields.creator": "creator", "parents": "container", "progress_percentage": "Work Done"}, "mitreTooltip": "Framework for mapping real-world cyber threats and attack techniques", "lindunnTooltip": "Framework for identifying and mitigating privacy risks", "abstractTooltip": "Brief summary of the case in 8 words", "accountFrameworks": {"nistHeader": "(NIST) SP 800-53 Rev. 5", "nistDescription": "This publication provides a catalog of security and privacy controls for information systems and organizations to protect organizational operations and assets, individuals, other organizations, and the Nation from a diverse set of threats and risks, including hostile attacks, human errors, natural disasters, structural failures, foreign intelligence entities, and privacy risks.", "highTrustHeader": "HITRUST Common Security Framework", "highTrustDescription": "The HITRUST Framework (HITRUST CSF®) has been adopted on a global scale for good reason. It is one, universal framework that maps to all critical control sets. It’s the most comprehensive, flexible, and efficient approach to managing the security risks of sensitive data and meeting regulatory compliance. The HITRUST Framework (HITRUST CSF) is designed to work with any number of sectors at any risk level, mapping security controls to harmonizing more than 50 authoritative sources.", "pciHeader": "Payment Card Industry Data Security Standard - Version 4", "pciDescription": "PCI DSS is a global security standard designed to protect cardholder data and secure payment card transactions against fraud. Created by the Payment Card Industry Security Standards Council, it provides a set of requirements for organizations that handle credit card information, ensuring the safe processing, storage, and transmission of cardholder data through comprehensive security measures.", "cisHeader": "CIS Critical Security Controls", "cisDescription": "The CIS Critical Security Controls are a set of best practices developed by the Center for Internet Security® (CIS®) to help organizations enhance their cybersecurity posture.\nIS Controls reflect the combined knowledge of experts from every part of the ecosystem, with every role, and across many sectors who have banded together to create, adopt, and\nsupport the CIS Controls."}, "incidentDescription": {"password": "Clear text password found", "private_key": "Private certificate found", "aws_key": "Sensitive token found"}, "codeSnippetDisclaimer": "This code example has been generated by AI", "implementationGuidance": "Implementation Guidance", "potentialSecurityViolation": "Potential Security Violations", "potentialViolations": "Potential Violations", "securityViolations": "Security Violations", "resetPassword": "reset password", "resetPasswordDescription": "We’ll send a reset password link to {{email}}", "sendResetLink": "Send Reset Link", "userResetSuccessfully": "Reset link was sent", "violationUpdatedSuccessfully": "Prime security violation updated successfully", "noPsv": "No prime security violations available", "labelCannotContainSpaces": "Label cannot contain spaces", "bulkEdit": "bulk edit", "knownIssue": "Known issue/Already addressed", "noSecurityImplications": "No security implications", "openedBySecurity": "Opened by Security - Ignore", "whatsNew": "What's New", "statusReview": "status review", "bulkUpdateSuccessful": "Cases updated successfully", "gotIt": "Got it", "thankYou": "Thank you", "psvSuccessMessage": "This will help us to improve your experience", "confluence": "confluence", "alreadyAware": "Already Aware", "fieldName": "field name", "fieldId": "field id", "values": "values", "fieldType": "field type", "noAttributes": "No jira attributes were found", "searchRecommendations": "Add your recommendation...", "tip": "tip", "settingsPage": "Settings page", "customRecommendationTip": "You can add a large number of recommendations by uploading a CSV file through the", "jiraAttributesTitle": "jira attributes", "jiraAttributesDescription": "Configure <PERSON><PERSON> attributes, turn them on and off", "systemActivityLogsTitle": "System Activity Logs", "systemActivityLogsDescription": "Set up and check system activity", "refresh": "refresh", "recommendationsImportedSuccessfully": "Recommendations imported successfully", "deleteRecommendationDescription": "Once deleted, this can't be undone. It will remove the recommendation from all the relevant cases.", "recommendationCannotBeEmpty": "Recommendation cannot be empty", "addTag": "Add Tag", "searchTags": "Search tags", "createTag": "Create", "exportToCsv": "Export to CSV", "errorTag": "Invalid tag", "csvImportSuccess": "CSV import successful", "attributesRefreshWarning": "This operation may take several minutes. You can check the progress on the task page", "any": "Any", "hasValue": "Has Value", "noValue": "No Value", "gDrive": "Google drive", "recommendationWrittenToJira": "Recommendation has been written to <PERSON><PERSON>.", "AddWatchersQuestion": "Do you want to be added to <PERSON>ra Issue as a watcher?", "addWatchersDialogTitle": "Add Yourself as a Watcher to <PERSON><PERSON> Issue", "addWatcherConfirm": "Add Me as a Watcher", "addWatcherSuccess": "Added watcher successfully", "upload": "Upload", "addWatchersConfirm": "Yes, Add watchers", "atlassian": "atlassian", "gDriveAccountDescription": "You are connected to Google Drive account", "gDriveDeleteWarning": "Any data associated with this source will be permanently deleted. You cannot undo this.", "changeAccount": "change account", "summaries": "summaries", "epics": "epics", "expectedOutcome": "Expected Outcome", "context": "Context", "goToTicketsView": "Go to Tickets View", "concerns": "Concerns", "noSummaryTitle": "No Summary, Yet", "noSummaryDescription": "Epic does not have enough content to generate a summary", "noContainerDescription": "Container does not have enough content to generate a summary", "noConcernsTitle": "No Concerns, Yet", "noConcernsDescription": "Epic does not have any concerns", "what": "What", "who": "Who", "where": "Where", "why": "Why", "how": "How", "insufficientInformation": "Insufficient information", "description": "description", "affected": "affected", "stakeholders": "stakeholders", "components": "components", "environment": "environment", "products": "products", "impact": "impact", "purpose": "purpose", "acceptance": "acceptance", "approach": "approach", "unexpectedError": "We've encountered an unexpected error. Our team has been notified and is working on a fix. Try to refresh the page.", "retry": "Retry", "goHome": "Go Home", "selected": "selected", "jira_issues_fetcher": "<PERSON><PERSON>er", "confluence_fetcher": "Confluence Fe<PERSON>er", "design_docs": "Design Docs", "classification": "Classification", "build_provider_fields_data": "Build provider fields data", "integrations": "Integrations", "createNotifications": "Create Notifications", "createNewNotification": "Create New Notification", "editNewNotification": "Edit Notification", "customViews": "Custom Views", "noCustomViewsFound": "No custom views found", "pickCustomViews": "Pick your custom views...", "dailyNotification": "Daily notification", "yourTimeZone": "Your time zone", "weeklyNotification": "Weekly notification", "pickYourDays": "Pick your days...", "noDaysFound": "No days found", "slackSettings": "<PERSON><PERSON><PERSON> Settings", "emailSettings": "<PERSON><PERSON>s", "pickYourChannels": "Pick your channels...", "noChannelsFound": "No channels found", "emailAddress": "Email Address", "done": "Done", "failedToRefreshChannels": "Failed to refresh channels", "channelsRefreshed": "Channels refreshed", "refreshChannels": "Refresh Channels", "documents": "Documents", "addDocument": "Add Document", "notificationTypes": {"digest_report": "Digest Report", "new_cases_alert": "New Cases Alert", "new_psv_alert": "New Security Violation Alert", "scan_started": "<PERSON><PERSON> Started", "scan_completed": "<PERSON>an Completed"}, "Linddun": {"Linkability": "Combining, associating, or linking data or actions to learn more about an individual or group, because the combined data provides more information and more insight", "Identifiability": "Identifying an individual (using available data, or actions)", "Non-repudiation": "The ability to link or attribute a claim (or action) to an individual", "Detectability": "Determining the involvement of an individual based on observation", "Disclosure of information": "Extreme or excessive collection, storage, processing, and/or sharing of personal data", "Unawareness": "Limited or insufficient notification, involvement, or empowerment for individuals regarding their (processing of) personal data", "Non-compliance": "Non-compliance to best practice security and data management"}, "Mitre": {"Collection": "Gathering sensitive data from compromised systems for exfiltration or further exploitation", "Command and Control": "Establishing communication channels with compromised systems for remote control", "Credential Access": "Stealing or cracking passwords and authentication tokens for further access", "Defense Evasion": "Avoiding detection by security tools through obfuscation, disabling defenses, or deleting logs", "Discovery": "Identifying system information, active users, and network settings to map out targets", "Execution": "Running malicious code on a victim’s system to achieve attack objectives", "Exfiltration": "Stealing data by transferring it outside the target environment", "Impact": "Disrupting, destroying, or manipulating data and systems to achieve malicious goals", "Initial Access": "Methods used to gain entry into a target system, like phishing or exploiting vulnerabilities", "Lateral Movement": "Expanding access within a network by moving between systems", "Persistence": "Techniques that ensure attackers maintain access to compromised systems, even after reboots", "Privilege Escalation": "Gaining higher-level permissions to access restricted data or functions", "Reconnaissance": "Adversaries gather information to plan future attacks, like scanning networks or searching for employee details", "Resource Development": "Attackers acquire resources such as domains, tools, or credentials to support their operations"}, "update_provider_fields": "Update provider fields", "samlAuthentication": "SAML Authentication", "samlAuthenticationDescription": "Fill the login details from the SAML provider", "xmlFileRequired": "XML file is required", "xmlFileUpload": "XML Document", "relayState": "Relay State", "pleaseAddSource": "Please add source", "containersView": "Containers View", "containers": "Containers", "container": "Container", "uploadPDFDocument": "Upload your PDF document", "browseFile": "Browse file", "browseFiles": "Browse files", "orDragItHere": "Or drag it here", "orDragThemHere": "Or drag them here", "supportsPDFOnly": "Supports PDF files only", "controls": "controls", "ssoDescription": "Enter the URL provided by your Identity Provider (IdP) for Single Sign-On (SSO)", "issuerDescription": "The unique identifier (Entity ID) of your Identity Provider (IdP)", "certificateDescription": "Paste the X.509 certificate from your Identity Provider (IdP) for SAML authentication", "checkStatusOfUploadedFiles": "Check the status of your uploaded files on the status page", "remove": "Remove", "generatePrimeRecommendations": "Generate Prime Recommendations", "recommendationsGeneratedSuccessfully": "Recommendations generated successfully, it may take a while to appear.", "viewBy": "view by", "insights": "insights", "confirm": "Confirm", "generate": "Generate", "columns": "columns", "selectView": "select view", "topRecommendations": "top recommendations", "askPrimeAI": "Ask Prime AI", "askPrime": "Ask Prime", "askPrimeAIPlaceholder": "Ask Prime AI...", "explanation": "explanation", "primeAI": "Prime AI (beta)", "clearChat": "Clear Chat", "hiHowCanIHelpYou": "Hi, how can I help you?", "noDataYet": "no data, yet", "connectASource": "Connect a source to start seeing insights on your dashboard", "sourceFile": "source file", "reference": "reference", "supportsFileTypes": "Supports {{types}} files only", "maxFiles": "Max {{count}} files", "noFilesUploaded": "No files uploaded", "noFilesFound": "No files found", "noFilesUploadedYet": "No files uploaded yet", "noFilesFoundYet": "No files found yet", "noFilesUploadedYetDescription": "Upload your PDF document", "noFilesFoundYetDescription": "Upload your PDF document", "securityAndCompliance": "Security & Compliance", "frameworks": "Frameworks", "companyGuidelines": "Company Guidelines", "uploadGuidelines": "Upload Guidelines", "uploadPDF": "Upload PDF", "uploadYourCompanySecurityGuidelines": "Upload your company security guidelines", "uploadGuidelinesDescription": "Upload your company security guidelines", "uploadGuidelinesPlaceholder": "Upload your company security guidelines", "uploadGuidelinesSuccess": "Guidelines uploaded successfully", "uploadGuidelinesError": "Failed to upload guidelines", "noFilesUploadedYetGuidelines": "No files uploaded yet, start by adding your first policy", "genDesignReview": "Generate design review", "genSecurityReview": "Generate security review", "thisActionMightTakeAWhile": "This action might take a while", "reviewStartedSuccessfully": "Generating review started successfully", "references": "references", "page": "page", "privacy": "privacy", "security": "security", "addCompanyPoliciesToKeepEverythingInOnePlace": "Add company policies to keep everything in one place", "guidelineDeletedSuccessfully": "Guideline deleted successfully", "thisWillPermanentlyDeleteGuideline": "This will permanently delete guidelines", "deleteGuideline": "Delete guideline", "noGuidelineSelected": "No guideline selected", "clickToEdit": "Click to edit", "fileTooLarge": "{{name}} exceeds the max file size of {{size}}.", "maxFileSize": "(Max: {{maxSize}} MB)", "addDesignReview": "add design review", "addSecurityReview": "add security review", "uploadFile": "upload file", "confluenceLink": "confluence link", "uploadLink": "upload link", "uploadLinkDescription": "Add links for Security Review", "confluenceLinkPlaceholder": "Paste a Confluence link", "newDesignReview": "new design review", "newSecurityReview": "new security review", "checkStatusOfUpload": "Check the status of your upload on the status page", "uploadLinkSuccessfully": "Uploaded link successfully", "documentsUploadSuccess": "{{docsNumber}} document(s) uploaded successfully", "confluenceLinkJobAlreadyRunning": "Confluence link upload already running", "invalidUrl": "please enter a valid url", "companyRecommendations": "company aligned recommendations", "policyReference": "references to company guidelines", "asPerPolicy": "As per policy", "relevantReference": "the relevant reference is", "lastUpdated": "Last updated", "designReview": "design review", "designReviews": "design reviews", "securityReview": "security review", "securityReviews": "security reviews", "viewUpdatedSuccessfully": "View updated successfully", "viewDeletedSuccessfully": "View deleted successfully", "googleDoc": "Google Docs", "googleLinkPlaceholder": "Paste a Google Docs link", "googleDocLink": "Google Docs Link", "advancedAnalysis": "advanced analysis", "attackVectors": "attack vectors", "dataflow": "dataflow", "policies": "policies", "addContext": "Add context", "attachFile": "Attach file", "addPolicy": "Add policy", "searchPolicies": "Search policies...", "noPolicyFound": "No policy found.", "containersMaps": "containers map", "designReviewType": "type", "askAI": "Ask AI...", "systemFlow": "system flow", "dataFlow": "data flow", "reviewAttackVectors": "review by attack vectors", "addDesignDoc": "Add design doc", "searchDesignDocs": "Search design docs...", "noDesignDocFound": "No design doc found.", "addContainers": "Add containers", "searchContainers": "Search containers...", "noContainersFound": "No containers found.", "child": "child", "children": "children", "reprocessSuccess": "Reprocess started successfully", "failedToReprocess": "Reprocess Failed", "analyzeAsOne": "Analyze as one", "quotes": "quotes", "goToWorkroom": "go to workroom", "issues": "issues", "byName": "by name", "addJiraProject": "Add Jira Project", "manageProjects": "Manage Projects", "jiraProjectsManagement": "Jira Projects Management", "jiraProjectsManagementDescription": "Pick projects to include in scanning. Note: These projects are excluded from the default scan flow.", "projectsWithPermission": "Projects with permission", "uploadNewVersion": "upload new version", "ready": "ready", "processing": "processing", "confirmReprocessMessage": "You are about to re-process the current design review. The information provided in the chat will be included. Confirm?", "goToSecurityReview": "go to security review", "mitigationPlanHistory": "mitigation plan history", "mitigationPlanCreatedSuccessfully": "Mitigation plan created successfully", "openMitigationPlan": "Open Mitigation Plan", "createMitigationPlan": "Create Mitigation Plan", "mitigationPlan": "Mitigation Plan", "mitigationPlanDescription": "Select the relevant recommendations and take action to mitigate them", "title": "title", "dueDate": "due date", "priority": "priority", "select": "select", "low": "low", "medium": "medium", "high": "high", "exportToPdf": "Export to PDF", "addCustomRecommendation": "Add Custom Recommendation", "customRecommendation": "Custom Recommendation", "customRecommendationDescription": "Add a custom recommendation", "customRecommendationPlaceholder": "Add a custom recommendation", "customRecommendationSuccess": "Custom recommendation added successfully", "customRecommendationError": "Failed to add custom recommendation", "customRecommendationAlreadyExists": "Custom recommendation already exists", "actions": "actions", "commentInJira": "Comment in Jira", "startTypingToSearchTickets": "Start typing to search tickets...", "selectTicket": "Select Ticket", "searchTickets": "Search tickets...", "noTicketsFound": "No tickets found", "searchCases": "Search cases...", "advancedMonitoringBoard": "advanced monitoring board", "advancedMonitoringBoardDescription": "Add and define filters for monitoring your board", "advancedMonitoring": "advanced monitoring", "ruleName": "Rule Name", "addRule": "add rule", "reviewRule": "Review Rule", "thisWillPermanentlyDeleteRule": "This will Permanently Delete Rule", "saveRule": "Save Rule", "pickFilter": "Pick a Filter", "ticketId": "ticket id"}