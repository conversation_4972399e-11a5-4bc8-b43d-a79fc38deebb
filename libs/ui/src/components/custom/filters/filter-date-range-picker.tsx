import { useEffect, useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { cn } from '@libs/common'
import type { DateRange } from 'react-day-picker'
import type { FilterItem } from './filter-interfaces'
import { format } from 'date-fns'
import { t } from 'i18next'
import {
  Button,
  Calendar,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../../base'
import { toast } from 'sonner'

interface DateRangePickerProps {
  title: string
  filterKey: string
  savedFilter?: FilterItem
  onFilterChange: (filter: FilterItem) => void
}

interface PresetProps {
  title: string
  from: Date
  to?: Date
}

export type DateRangePreset =
  | 'past7Days'
  | 'past14Days'
  | 'past30Days'
  | 'past6Months'
  | 'pastYear'
  | 'custom'

const presets: Record<DateRangePreset, PresetProps> = {
  past7Days: {
    title: 'Past 7 days',
    from: new Date(new Date().getTime() - 6 * 24 * 60 * 60 * 1000),
    to: new Date(),
  },
  past14Days: {
    title: 'Past 14 days',
    from: new Date(new Date().getTime() - 13 * 24 * 60 * 60 * 1000),
    to: new Date(),
  },
  past30Days: {
    title: 'Past 30 days',
    from: new Date(new Date().getTime() - 29 * 24 * 60 * 60 * 1000),
    to: new Date(),
  },
  past6Months: {
    title: 'Past 6 months',
    from: new Date(new Date().getFullYear(), new Date().getMonth() - 5, 1),
    to: new Date(),
  },
  pastYear: {
    title: 'Past year',
    from: new Date(new Date().getFullYear() - 1, 0, 1),
    to: new Date(),
  },
  custom: {
    title: 'Custom',
    from: new Date(new Date().getTime() - 6 * 24 * 60 * 60 * 1000),
    to: new Date(),
  },
}

export const FilterDateRangePicker = ({
  filterKey,
  title,
  savedFilter,
  onFilterChange,
}: DateRangePickerProps) => {
  const [isOpen, setIsOpen] = useState(false)

  const [isPopoverOpen, setIsPopoverOpen] = useState(false)
  const [tempDate, setTempDate] = useState<DateRange | undefined>(
    savedFilter
      ? {
          from: new Date(savedFilter.value[0]),
          to: new Date(savedFilter.value[1]),
        }
      : presets.past30Days
  )
  const [preset, setPreset] = useState<DateRangePreset | null>(
    savedFilter ? 'custom' : 'past30Days'
  )

  const handleDateRangePreset = (preset: DateRangePreset) => {
    setPreset(preset)
    setTempDate(presets[preset])
  }

  const handleApply = () => {
    if (!tempDate) {
      return
    }

    if (!tempDate.from || !tempDate.to) {
      toast.error('Please select a valid date range')
      return
    }
    const from = tempDate?.from?.toISOString() || ''
    const to = tempDate?.to?.toISOString() || ''
    onFilterChange({
      field: filterKey,
      value: [from, to],
      op: 'between',
    })
    setIsPopoverOpen(false)
  }

  const handleClear = () => {
    setTempDate(presets.past30Days)
  }

  useEffect(() => {
    if (savedFilter) {
      setTempDate({
        from: new Date(savedFilter?.value[0]),
        to: new Date(savedFilter?.value[1]),
      })
      setPreset('custom')
    }
  }, [savedFilter])

  return (
    <div className={cn('grid gap-2')}>
      <Collapsible
        className={isOpen ? 'border-b border-gray-200 mb-2 pb-2' : ''}
        open={isOpen}
        onOpenChange={setIsOpen}
      >
        <CollapsibleTrigger asChild>
          <div
            className={cn(
              'flex justify-between items-center gap-3 cursor-pointer capitalize mb-3 font-medium pb-2',
              isOpen ? '' : 'border-b border-gray-200'
            )}
          >
            <div className="flex gap-2 text-lg">
              <span>{title}</span>
              {savedFilter && <span>(1)</span>}
            </div>
            <div className="flex items-center gap-1">
              {isOpen ? (
                <ChevronUp className="h-5 w-5" />
              ) : (
                <ChevronDown className="h-5 w-5" />
              )}
            </div>
          </div>
        </CollapsibleTrigger>
        <CollapsibleContent
          className="popover-content w-[300px] p-0 flex flex-row"
          onClick={(e) => e.stopPropagation()}
        >
          <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
            <PopoverTrigger asChild>
              <Button
                id="date"
                variant="outline"
                dataTestId="date-range-picker-button"
                className={cn('h-8 capitalize text-sm outline-none pl-4 pr-2')}
              >
                <div className="flex items-center">
                  {savedFilter ? (
                    <div>
                      {format(savedFilter.value[0], 'LLL dd, y')} -{' '}
                      {format(savedFilter.value[1], 'LLL dd, y')}
                    </div>
                  ) : (
                    <div>{t('selectDateRange')}</div>
                  )}
                  <ChevronDown className="ml-4" />
                </div>
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className="popover-content w-auto p-0 flex flex-row"
              align="start"
            >
              <div className="presets-list flex flex-col justify-start items-start gap-1 p-2">
                {Object.keys(presets).map((key) => (
                  <Button
                    key={crypto.randomUUID()}
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      handleDateRangePreset(key as DateRangePreset)
                    }
                    className={cn(
                      'w-full text-left font-normal justify-start',
                      preset === key && 'bg-accent'
                    )}
                    dataTestId="date-range-preset-button"
                  >
                    {presets?.[key as DateRangePreset].title}
                  </Button>
                ))}
              </div>
              <div className="calendar-wrapper">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={tempDate?.from}
                  selected={tempDate || { from: undefined, to: undefined }}
                  onSelect={(value: { from?: Date; to?: Date } | undefined) => {
                    if (value?.from != null) {
                      setTempDate({ from: value.from, to: value?.to })
                    }
                  }}
                  numberOfMonths={2}
                />
                <div className="p-4 flex justify-between">
                  <Button
                    size="sm"
                    onClick={handleClear}
                    variant="secondary"
                    className="capitalize"
                    dataTestId="clear-date-range"
                  >
                    {t('clear')}
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleApply}
                    className="capitalize"
                    disabled={!tempDate || !tempDate.from || !tempDate.to}
                    dataTestId="apply-date-range"
                  >
                    {t('apply')}
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}
